import { describe, it, expect, vi } from 'vitest';
import {
  formatNumber,
  formatCurrency,
  formatPercent,
  formatDate,
  validateDateRange,
  validateAmount,
  validatePercentage,
  getMonthsBetween,
  getDaysBetween,
  calculateAnnualizedReturn,
  calculateVolatility,
  calculateMaxDrawdown,
  calculateSharpeRatio,
  debounce,
  throttle,
  deepClone,
  generateId,
  handleError,
} from '@/lib/utils';

describe('Utils - 格式化函数', () => {
  describe('formatNumber', () => {
    it('应该正确格式化数字', () => {
      expect(formatNumber(1234.567)).toBe('1,234.57');
      expect(formatNumber(1234.567, 3)).toBe('1,234.567');
      expect(formatNumber(0)).toBe('0.00');
    });

    it('应该处理NaN值', () => {
      expect(formatNumber(NaN)).toBe('--');
    });
  });

  describe('formatCurrency', () => {
    it('应该正确格式化货币', () => {
      expect(formatCurrency(1234.567)).toBe('¥1,234.57');
      expect(formatCurrency(0)).toBe('¥0.00');
    });

    it('应该处理NaN值', () => {
      expect(formatCurrency(NaN)).toBe('--');
    });
  });

  describe('formatPercent', () => {
    it('应该正确格式化百分比', () => {
      expect(formatPercent(0.1234)).toBe('12.34%');
      expect(formatPercent(0.1234, 1)).toBe('12.3%');
      expect(formatPercent(0)).toBe('0.00%');
    });

    it('应该处理NaN值', () => {
      expect(formatPercent(NaN)).toBe('--');
    });
  });

  describe('formatDate', () => {
    it('应该正确格式化日期', () => {
      const date = new Date('2024-01-15');
      expect(formatDate(date)).toBe('2024-01-15');
      expect(formatDate('2024-01-15')).toBe('2024-01-15');
    });

    it('应该处理无效日期', () => {
      expect(formatDate('invalid')).toBe('--');
    });
  });
});

describe('Utils - 验证函数', () => {
  describe('validateDateRange', () => {
    it('应该验证有效的日期范围', () => {
      expect(() => validateDateRange('2024-01-01', '2024-01-31')).not.toThrow();
    });

    it('应该拒绝无效的开始日期', () => {
      expect(() => validateDateRange('invalid', '2024-01-31')).toThrow('无效的开始日期');
    });

    it('应该拒绝开始日期晚于结束日期', () => {
      expect(() => validateDateRange('2024-01-31', '2024-01-01')).toThrow('开始日期不能晚于结束日期');
    });
  });

  describe('validateAmount', () => {
    it('应该验证有效金额', () => {
      expect(() => validateAmount(1000)).not.toThrow();
    });

    it('应该拒绝负数', () => {
      expect(() => validateAmount(-100)).toThrow('金额不能为负数');
    });

    it('应该拒绝过小的金额', () => {
      expect(() => validateAmount(0.5)).toThrow('金额不能小于1元');
    });
  });

  describe('validatePercentage', () => {
    it('应该验证有效百分比', () => {
      expect(() => validatePercentage(50)).not.toThrow();
    });

    it('应该拒绝超出范围的百分比', () => {
      expect(() => validatePercentage(-10)).toThrow('百分比必须在0-100之间');
      expect(() => validatePercentage(110)).toThrow('百分比必须在0-100之间');
    });
  });
});

describe('Utils - 日期计算函数', () => {
  describe('getMonthsBetween', () => {
    it('应该正确计算月份差', () => {
      expect(getMonthsBetween('2024-01-01', '2024-03-01')).toBe(2);
      expect(getMonthsBetween('2024-01-01', '2025-01-01')).toBe(12);
    });
  });

  describe('getDaysBetween', () => {
    it('应该正确计算天数差', () => {
      expect(getDaysBetween('2024-01-01', '2024-01-02')).toBe(1);
      expect(getDaysBetween('2024-01-01', '2024-01-31')).toBe(30);
    });
  });
});

describe('Utils - 金融计算函数', () => {
  describe('calculateAnnualizedReturn', () => {
    it('应该正确计算年化收益率', () => {
      const result = calculateAnnualizedReturn(1.0, 1.1, 365);
      expect(result).toBeCloseTo(10, 1);
    });

    it('应该处理无效输入', () => {
      expect(calculateAnnualizedReturn(0, 1.1, 365)).toBe(0);
      expect(calculateAnnualizedReturn(1.0, 0, 365)).toBe(0);
    });
  });

  describe('calculateVolatility', () => {
    it('应该正确计算波动率', () => {
      const returns = [0.01, -0.02, 0.03, -0.01, 0.02];
      const volatility = calculateVolatility(returns);
      expect(volatility).toBeGreaterThan(0);
    });

    it('应该处理空数组', () => {
      expect(calculateVolatility([])).toBe(0);
    });
  });

  describe('calculateMaxDrawdown', () => {
    it('应该正确计算最大回撤', () => {
      const values = [100, 110, 105, 95, 120];
      const maxDrawdown = calculateMaxDrawdown(values);
      expect(maxDrawdown).toBeCloseTo(13.64, 1); // (110-95)/110 * 100
    });

    it('应该处理空数组', () => {
      expect(calculateMaxDrawdown([])).toBe(0);
      expect(calculateMaxDrawdown([100])).toBe(0);
    });
  });

  describe('calculateSharpeRatio', () => {
    it('应该正确计算夏普比率', () => {
      const sharpe = calculateSharpeRatio(10, 15, 0.03);
      expect(sharpe).toBeCloseTo(0.47, 1);
    });

    it('应该处理零波动率', () => {
      expect(calculateSharpeRatio(10, 0)).toBe(0);
    });
  });
});

describe('Utils - 工具函数', () => {
  describe('debounce', () => {
    it('应该延迟执行函数', async () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      expect(mockFn).not.toHaveBeenCalled();

      await new Promise(resolve => setTimeout(resolve, 150));
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('throttle', () => {
    it('应该限制函数执行频率', async () => {
      const mockFn = vi.fn();
      const throttledFn = throttle(mockFn, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(mockFn).toHaveBeenCalledTimes(1);

      await new Promise(resolve => setTimeout(resolve, 150));
      throttledFn();
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
  });

  describe('deepClone', () => {
    it('应该深拷贝对象', () => {
      const original = { a: 1, b: { c: 2 }, d: [3, 4] };
      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.b).not.toBe(original.b);
      expect(cloned.d).not.toBe(original.d);
    });

    it('应该处理特殊值', () => {
      expect(deepClone(null)).toBe(null);
      expect(deepClone(undefined)).toBe(undefined);
      expect(deepClone(42)).toBe(42);
      expect(deepClone('string')).toBe('string');
    });
  });

  describe('generateId', () => {
    it('应该生成唯一ID', () => {
      const id1 = generateId();
      const id2 = generateId();
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^id_\d+_[a-z0-9]+$/);
    });

    it('应该使用自定义前缀', () => {
      const id = generateId('test');
      expect(id).toMatch(/^test_\d+_[a-z0-9]+$/);
    });
  });

  describe('handleError', () => {
    it('应该处理Error对象', () => {
      const error = new Error('Test error');
      expect(handleError(error)).toBe('Test error');
    });

    it('应该处理字符串错误', () => {
      expect(handleError('String error')).toBe('String error');
    });

    it('应该处理未知错误', () => {
      expect(handleError({})).toBe('未知错误');
    });
  });
});
