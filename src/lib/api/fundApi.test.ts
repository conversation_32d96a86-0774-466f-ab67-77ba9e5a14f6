import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { FundApiService, fetchFundData, getFundBasicInfo } from '@/lib/api/fundApi';
import type { FundData } from '@/types/fund';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock console to reduce noise
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};
global.console = mockConsole as any;

describe('FundApiService', () => {
  let apiClient: FundApiService;

  beforeEach(() => {
    apiClient = new FundApiService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getFundData', () => {
    const mockApiResponse = {
      ResultCode: '0',
      Result: {
        DisplayData: [
          {
            date: '2024-01-01',
            netAssetValue: '1.0000',
            accumulatedValue: '1.0000',
          },
          {
            date: '2024-01-02',
            netAssetValue: '1.0100',
            accumulatedValue: '1.0100',
          },
        ],
      },
    };

    it('应该成功获取基金数据', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse),
      });

      const result = await apiClient.getFundData('000628', '2024-01-01', '2024-01-02');

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(2);
      expect(result[0]).toHaveProperty('date');
      expect(result[0]).toHaveProperty('netAssetValue');
      expect(result[0]).toHaveProperty('accumulatedValue');
    });

    it('应该验证基金代码格式', async () => {
      await expect(
        apiClient.getFundData('invalid', '2024-01-01', '2024-01-02')
      ).rejects.toThrow('无效的基金代码');
    });

    it('应该验证日期范围', async () => {
      await expect(
        apiClient.getFundData('000628', '2024-12-31', '2024-01-01')
      ).rejects.toThrow('开始日期不能晚于结束日期');
    });

    it('应该处理API错误响应', async () => {
      const errorResponse = {
        ResultCode: '1',
        Result: null,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(errorResponse),
      });

      await expect(
        apiClient.getFundData('000628', '2024-01-01', '2024-01-02')
      ).rejects.toThrow('API错误');
    }, 10000);

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(
        apiClient.getFundData('000628', '2024-01-01', '2024-01-02')
      ).rejects.toThrow();
    }, 10000);

    it('应该处理HTTP错误状态', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      });

      await expect(
        apiClient.getFundData('000628', '2024-01-01', '2024-01-02')
      ).rejects.toThrow('HTTP 404');
    }, 10000);
  });

  describe('缓存功能', () => {
    const mockApiResponse = {
      ResultCode: '0',
      Result: {
        DisplayData: [
          {
            date: '2024-01-01',
            netAssetValue: '1.0000',
            accumulatedValue: '1.0000',
          },
        ],
      },
    };

    it('应该缓存API响应', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockApiResponse),
      });

      // 第一次调用
      await apiClient.getFundData('000628', '2024-01-01', '2024-01-02');
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // 第二次调用应该使用缓存
      await apiClient.getFundData('000628', '2024-01-01', '2024-01-02');
      expect(mockFetch).toHaveBeenCalledTimes(1); // 仍然是1次，说明使用了缓存
    });

    it('应该支持禁用缓存', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockApiResponse),
      });

      // 第一次调用
      await apiClient.getFundData('000628', '2024-01-01', '2024-01-02', { useCache: false });
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // 第二次调用不使用缓存
      await apiClient.getFundData('000628', '2024-01-01', '2024-01-02', { useCache: false });
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('重试机制', () => {
    it('应该在失败时重试', async () => {
      // 前两次失败，第三次成功
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ResultCode: '0',
            Result: { DisplayData: [] },
          }),
        });

      const result = await apiClient.getFundData('000628', '2024-01-01', '2024-01-02', {
        retryCount: 3,
        retryDelay: 10, // 减少延迟以加快测试
      });

      expect(result).toBeDefined();
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('应该在达到最大重试次数后抛出错误', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(
        apiClient.getFundData('000628', '2024-01-01', '2024-01-02', {
          retryCount: 2,
          retryDelay: 10,
        })
      ).rejects.toThrow('Network error');

      expect(mockFetch).toHaveBeenCalledTimes(3); // 初始调用 + 2次重试
    });
  });

  describe('数据转换', () => {
    it('应该正确转换API响应数据', async () => {
      const mockApiResponse = {
        ResultCode: '0',
        Result: {
          DisplayData: [
            {
              date: '2024-01-01',
              netAssetValue: '1.0000',
              accumulatedValue: '1.0000',
            },
            {
              date: '2024-01-02',
              netAssetValue: '1.0100',
              accumulatedValue: '1.0100',
            },
          ],
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse),
      });

      const result = await apiClient.getFundData('000628', '2024-01-01', '2024-01-02');

      expect(result[0].netAssetValue).toBe(1.0000);
      expect(result[1].netAssetValue).toBe(1.0100);
      expect(typeof result[0].netAssetValue).toBe('number');
    });

    it('应该处理无效的数值', async () => {
      const mockApiResponse = {
        ResultCode: '0',
        Result: {
          DisplayData: [
            {
              date: '2024-01-01',
              netAssetValue: 'invalid',
              accumulatedValue: '1.0000',
            },
          ],
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse),
      });

      const result = await apiClient.getFundData('000628', '2024-01-01', '2024-01-02');

      // 应该过滤掉无效数据或设置默认值
      expect(result.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('边界情况', () => {
    it('应该处理空的API响应', async () => {
      const mockApiResponse = {
        ResultCode: '0',
        Result: {
          DisplayData: [],
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse),
      });

      const result = await apiClient.getFundData('000628', '2024-01-01', '2024-01-02');

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });

    it('应该处理超时', async () => {
      // 模拟超时
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );

      await expect(
        apiClient.getFundData('000628', '2024-01-01', '2024-01-02', {
          timeout: 50,
        })
      ).rejects.toThrow();
    });
  });
});
